services:
  db:
    image: mariadb:10.11
    restart: unless-stopped
    environment:
      MYS<PERSON>_RANDOM_ROOT_PASSWORD: "true"
      MYSQL_DATABASE: ${DATASOURCES_DEFAULT_DATABASE}
      MYSQL_USER: ${DATASOURCES_DEFAULT_USERNAME}
      MYSQL_PASSWORD: ${DATASOURCES_DEFAULT_PASSWORD}
    volumes:
      - database_volume:/var/lib/mysql
    networks:
      - default

  passbolt:
    image: passbolt/passbolt:latest-ce
    #Alternatively you can use rootless:
    #image: passbolt/passbolt:latest-ce-non-root
    restart: unless-stopped
    depends_on:
      - db
    environment:
      APP_FULL_BASE_URL: https://passbolt.otechq.com
      DATASOURCES_DEFAULT_HOST: "db"
      DATASOURCES_DEFAULT_USERNAME: ${DATASOURCES_DEFAULT_USERNAME}
      DATASOURCES_DEFAULT_PASSWORD: ${DATASOURCES_DEFAULT_PASSWORD}
      DATASOURCES_DEFAULT_DATABASE: ${DATASOURCES_DEFAULT_DATABASE}
      # SMTP Configuration
      EMAIL_TRANSPORT_DEFAULT_HOST: ${EMAIL_TRANSPORT_DEFAULT_HOST}
      EMAIL_TRANSPORT_DEFAULT_PORT: ${EMAIL_TRANSPORT_DEFAULT_PORT}
      EMAIL_TRANSPORT_DEFAULT_USERNAME: ${EMAIL_TRANSPORT_DEFAULT_USERNAME}
      EMAIL_TRANSPORT_DEFAULT_PASSWORD: ${EMAIL_TRANSPORT_DEFAULT_PASSWORD}
      EMAIL_TRANSPORT_DEFAULT_TLS: "true"
      EMAIL_DEFAULT_FROM: ${EMAIL_DEFAULT_FROM}
      EMAIL_DEFAULT_FROM_NAME: "Passbolt Notification"
    volumes:
      - gpg_volume:/etc/passbolt/gpg
      - jwt_volume:/etc/passbolt/jwt
    command:
      [
        "/usr/bin/wait-for.sh",
        "-t",
        "0",
        "db:3306",
        "--",
        "/docker-entrypoint.sh",
      ]
    networks:
      - default
      - dokploy-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.passbolt.rule=Host(`passbolt.otechq.com`)"
      - "traefik.http.routers.passbolt.entrypoints=websecure"
      - "traefik.http.routers.passbolt.tls.certresolver=letsencrypt"
      - "traefik.http.routers.passbolt-http.rule=Host(`passbolt.otechq.com`)"
      - "traefik.http.routers.passbolt-http.entrypoints=web"
      - "traefik.http.services.passbolt.loadbalancer.server.port=80"
      - "traefik.docker.network=dokploy-network"
    # ports:
    #   - 80:80
    #   - 443:443
    #Alternatively for non-root images:
    # - 80:8080
    # - 443:4433

volumes:
  database_volume:
  gpg_volume:
  jwt_volume:
networks:
  dokploy-network:
    external: true
  default:
    driver: bridge
