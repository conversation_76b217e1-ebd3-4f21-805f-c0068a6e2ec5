# Passbolt Password Manager Deployment

This repository contains a Docker Compose setup for deploying [Passbolt](https://www.passbolt.com/), an open-source password manager, with MariaDB database, Traefik reverse proxy, and Let's Encrypt SSL certificates.

## 🚀 Features

- **Passbolt Community Edition**: Latest stable version
- **MariaDB Database**: Persistent data storage
- **Traefik Integration**: Automatic reverse proxy and SSL certificates
- **Let's Encrypt**: Automatic HTTPS certificate management
- **Email Notifications**: SMTP configuration for user notifications
- **Production Ready**: Configured for production deployment

## 📋 Prerequisites

- Docker and Docker Compose installed
- Domain name pointing to your server (passbolt.otechq.com)
- Traefik reverse proxy running with Dokploy network
- SMTP credentials for email notifications

## 🛠️ Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd passbolt
```

### 2. Configure Environment Variables

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:

```env
# Application URL
APP_FULL_BASE_URL=https://your-domain.com

# Database Configuration
DATASOURCES_DEFAULT_USERNAME=passbolt
DATASOURCES_DEFAULT_PASSWORD=your-secure-password
DATASOURCES_DEFAULT_DATABASE=passbolt

# SMTP Configuration
EMAIL_TRANSPORT_DEFAULT_HOST=smtp.gmail.com
EMAIL_TRANSPORT_DEFAULT_PORT=587
EMAIL_TRANSPORT_DEFAULT_USERNAME=<EMAIL>
EMAIL_TRANSPORT_DEFAULT_PASSWORD=your-app-password
EMAIL_DEFAULT_FROM=<EMAIL>
```

### 3. Deploy the Application

```bash
docker-compose -f docker-compose-ce.yaml up -d
```

### 4. Verify Deployment

Check if all services are running:

```bash
docker-compose -f docker-compose-ce.yaml ps
```

## 🔧 Configuration

### Environment Variables

| Variable                           | Description                                | Example                        |
| ---------------------------------- | ------------------------------------------ | ------------------------------ |
| `APP_FULL_BASE_URL`                | Full URL where Passbolt will be accessible | `https://passbolt.example.com` |
| `DATASOURCES_DEFAULT_USERNAME`     | Database username                          | `passbolt`                     |
| `DATASOURCES_DEFAULT_PASSWORD`     | Database password                          | `secure-password`              |
| `DATASOURCES_DEFAULT_DATABASE`     | Database name                              | `passbolt`                     |
| `EMAIL_TRANSPORT_DEFAULT_HOST`     | SMTP server hostname                       | `smtp.gmail.com`               |
| `EMAIL_TRANSPORT_DEFAULT_PORT`     | SMTP server port                           | `587`                          |
| `EMAIL_TRANSPORT_DEFAULT_USERNAME` | SMTP username                              | `<EMAIL>`               |
| `EMAIL_TRANSPORT_DEFAULT_PASSWORD` | SMTP password/app password                 | `app-password`                 |
| `EMAIL_DEFAULT_FROM`               | Default sender email                       | `<EMAIL>`               |

### Traefik Labels

The application is configured with Traefik labels for:

- Automatic service discovery
- HTTPS redirection
- Let's Encrypt certificate generation
- Load balancing

## 📁 Project Structure

```bash
.
├── docker-compose-ce.yaml     # Main Docker Compose configuration
├── docker-compose-ce-SHA512SUM.txt  # Checksum verification
├── .env                       # Environment variables
├── .gitignore                # Git ignore rules
├── .augmentignore            # Augment ignore rules
└── README.md                 # This documentation
```

## 🔒 Security Considerations

1. **Environment Variables**: Keep your `.env` file secure and never commit sensitive data
2. **Database Password**: Use a strong, unique password for the database
3. **SMTP Credentials**: Use app-specific passwords for email services
4. **SSL/TLS**: Ensure HTTPS is properly configured via Traefik
5. **Firewall**: Configure appropriate firewall rules for your server

## 🚀 Usage

1. Access Passbolt at your configured domain (e.g., <https://passbolt.otechq.com>)
2. Create your first admin user (see detailed steps below)
3. Configure GPG keys for encryption
4. Generate JWT keys for mobile app connectivity (see below)
5. Start managing your passwords securely

### Creating the First Admin User

After deploying Passbolt, you need to create the first admin user using the command line:

```bash
# 1. Open a shell in the Passbolt container
docker exec -it passbolt bash

# 2. Create the first admin user
./bin/cake passbolt:user:create

# Follow the interactive prompts:
# - Enter first name
# - Enter last name
# - Enter email address
# - Choose role: admin

# 3. Exit the container
exit
```

**Alternative method using command line arguments:**

```bash
# Create admin user with all details in one command
docker exec -it passbolt bash -c "./bin/cake passbolt:user:create \
  --username='<EMAIL>' \
  --first-name='Admin' \
  --last-name='User' \
  --role='admin'"
```

**Important Notes:**

- The email address will be used as the username for login
- You'll receive an email invitation to complete the account setup
- Make sure your SMTP configuration is working before creating users
- The first user should always have admin role to manage the system

**Complete the Setup:**

1. Check your email for the setup invitation
2. Click the link in the email to complete registration
3. Create your GPG key pair during the setup process
4. Set your master password
5. You're now ready to use Passbolt!

### Mobile App Setup (JWT Configuration)

To connect Passbolt mobile apps, you need to generate JWT keys:

```bash
# 1. Open a shell in the Passbolt container
docker exec -it passbolt bash

# 2. Generate the private key
openssl genrsa -out /etc/passbolt/jwt/jwt.pem 4096

# 3. Generate the public key
openssl rsa -in /etc/passbolt/jwt/jwt.pem -pubout -out /etc/passbolt/jwt/jwt.pub

# 4. Set correct permissions
chmod 600 /etc/passbolt/jwt/jwt.pem /etc/passbolt/jwt/jwt.pub
chown www-data:www-data /etc/passbolt/jwt/jwt.pem /etc/passbolt/jwt/jwt.pub

# 5. Exit container
exit

# 6. Restart Passbolt to apply changes
docker-compose -f docker-compose-ce.yaml restart passbolt
```

After generating the JWT keys, you can:

- Download the Passbolt mobile app from your app store
- Use your domain URL and credentials to connect
- Enjoy secure password management on mobile devices

## 🔄 Maintenance

### Backup

Regular backups are essential:

```bash
# Backup database
docker-compose -f docker-compose-ce.yaml exec db mysqldump -u passbolt -p passbolt > backup.sql

# Backup volumes
docker run --rm -v passbolt_gpg_volume:/data -v $(pwd):/backup alpine tar czf /backup/gpg-backup.tar.gz -C /data .
docker run --rm -v passbolt_jwt_volume:/data -v $(pwd):/backup alpine tar czf /backup/jwt-backup.tar.gz -C /data .
```

### Updates

To update Passbolt:

```bash
docker-compose -f docker-compose-ce.yaml pull
docker-compose -f docker-compose-ce.yaml up -d
```

### Logs

View application logs:

```bash
# All services
docker-compose -f docker-compose-ce.yaml logs -f

# Specific service
docker-compose -f docker-compose-ce.yaml logs -f passbolt
```

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Issues**

   - Verify database credentials in `.env`
   - Check if database container is running
   - Ensure network connectivity between containers

2. **Email Not Working**

   - Verify SMTP credentials
   - Check if your email provider requires app-specific passwords
   - Review email logs in Passbolt container

3. **SSL Certificate Issues**
   - Ensure domain DNS is pointing to your server
   - Check Traefik logs for certificate generation
   - Verify Let's Encrypt rate limits

### Getting Help

- Check container logs: `docker-compose -f docker-compose-ce.yaml logs`
- Passbolt Documentation: <https://help.passbolt.com/>
- Community Forum: <https://community.passbolt.com/>

## 📄 License

This deployment configuration is provided as-is. Passbolt itself is licensed under AGPL v3.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test the deployment
5. Submit a pull request

---

**Note**: This setup is configured for production use with Traefik and Let's Encrypt. Ensure you have proper monitoring and backup procedures in place.
